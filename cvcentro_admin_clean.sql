-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump - CLEANED FOR PRODUCTION TESTING
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generated: Production Clean Version
-- Server version: 8.0.43
-- PHP Version: 8.3.23
--
-- SECURITY NOTICE: This is a cleaned database with all data removed except essential users
-- Use this for production testing and development environments
-- DO NOT use the original database file in production without proper data sanitization

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `cvcentro_admin`
--

-- --------------------------------------------------------

--
-- Table structure for table `acquisto_amico`
--

CREATE TABLE `acquisto_amico` (
  `id_acq` int NOT NULL,
  `id_cliente` int NOT NULL,
  `id_amico` int NOT NULL,
  `prodotto_acq` varchar(255) NOT NULL,
  `somma_acq` int NOT NULL,
  `data_acq` date NOT NULL,
  `dip_acq` varchar(255) NOT NULL,
  `somma_buono` int NOT NULL,
  `scontrino_buono` varchar(11) NOT NULL,
  `status_buono` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `acquisto_weekbuy`
--

CREATE TABLE `acquisto_weekbuy` (
  `id_acq_wb` int NOT NULL,
  `id_cliente` int NOT NULL,
  `prodotto` varchar(255) NOT NULL,
  `somma` int NOT NULL,
  `data_acq_wb` date NOT NULL,
  `status_acq_wb` varchar(255) NOT NULL,
  `consegnato_wb` int NOT NULL,
  `dipendente` varchar(255) NOT NULL,
  `colore_wb` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `assicurazione`
--

CREATE TABLE `assicurazione` (
  `id_assicurazione` int NOT NULL,
  `nome_assicurazione` varchar(255) DEFAULT NULL,
  `prezzo_assicurazione` varchar(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `assicurazione_cliente`
--

CREATE TABLE `assicurazione_cliente` (
  `id_assicurazione_cliente` int NOT NULL,
  `id_cliente` int NOT NULL,
  `id_assicurazione` int NOT NULL,
  `id_cellulare` int NOT NULL,
  `dipendente` varchar(255) DEFAULT NULL,
  `data_registrazione` date NOT NULL,
  `status_assicurazione` varchar(50) NOT NULL DEFAULT 'Attiva',
  `data_utilizzo` date DEFAULT NULL,
  `motivazione_utilizzo` text,
  `imei` varchar(50) DEFAULT NULL,
  `scontrino` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `assistenza`
--

CREATE TABLE `assistenza` (
  `id_assistenza` int NOT NULL,
  `id_cliente_assistenza` int NOT NULL,
  `data_assistenza` datetime NOT NULL,
  `id_cell_assistenza` int NOT NULL,
  `scontrino_assistenza` varchar(255) NOT NULL,
  `garanzia_assistenza` varchar(255) NOT NULL DEFAULT 'no',
  `data_garanzia_assistenza` date DEFAULT NULL,
  `imei_assistenza` varchar(255) NOT NULL,
  `apple_assistenza` varchar(255) NOT NULL,
  `applepass_assistenza` varchar(255) NOT NULL,
  `pin_assistenza` varchar(255) NOT NULL,
  `annotazioni_assistenza` varchar(255) NOT NULL,
  `accessori_assistenza` varchar(255) NOT NULL,
  `difetto_assistenza` text NOT NULL,
  `firma_assistenza` varchar(280) NOT NULL,
  `dipendente_assistenza` varchar(255) NOT NULL,
  `status_assistenza` int NOT NULL DEFAULT '0',
  `prezzo_assistenza` int NOT NULL,
  `acconto_assistenza` int NOT NULL,
  `data_consegna_assistenza` date DEFAULT NULL,
  `data_ritiro_assistenza` date DEFAULT NULL,
  `lab_assistenza` int NOT NULL DEFAULT '0',
  `costo_lab_assistenza` int NOT NULL DEFAULT '0',
  `data_lab_assistenza` date DEFAULT NULL,
  `data_rientro_assistenza` date DEFAULT NULL,
  `note_assistenza` text,
  `modo_pagamento` varchar(50) DEFAULT 'Contanti'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `assistenza_cell`
--

CREATE TABLE `assistenza_cell` (
  `id_ass_cell` int NOT NULL,
  `nome_ass_cell` varchar(255) NOT NULL,
  `code_ass_cell` varchar(20) NOT NULL,
  `marca_ass_cell` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `buoni`
--

CREATE TABLE `buoni` (
  `id_buono` int NOT NULL,
  `id_acquisto` int NOT NULL,
  `id_cliente_buono` int NOT NULL,
  `somma_buono` int NOT NULL,
  `status_buono` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `buoni_banco`
--

CREATE TABLE `buoni_banco` (
  `id_buono` int NOT NULL,
  `id_cliente` int NOT NULL,
  `databuono` date NOT NULL,
  `acconto` int NOT NULL,
  `motivo` varchar(255) NOT NULL,
  `dipendente` varchar(50) NOT NULL,
  `firma` varchar(255) NOT NULL,
  `status_buono` varchar(55) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cellulare`
--

CREATE TABLE `cellulare` (
  `id_cell` int NOT NULL,
  `nome_cell` varchar(255) DEFAULT NULL,
  `prezzo_cell` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cliente`
--

CREATE TABLE `cliente` (
  `id_cliente` int NOT NULL,
  `nome_cliente` varchar(288) DEFAULT NULL,
  `telefono_cliente` varchar(255) DEFAULT NULL,
  `id_telefono` int NOT NULL DEFAULT '1',
  `data_cliente` datetime DEFAULT NULL,
  `nascita_cliente` date NOT NULL,
  `email_cliente` varchar(288) DEFAULT NULL,
  `numero_dip` varchar(255) DEFAULT NULL,
  `firma_cliente` int DEFAULT '0',
  `indirizzo_cliente` varchar(255) NOT NULL,
  `citta_cliente` varchar(255) NOT NULL,
  `prov_cliente` varchar(255) NOT NULL,
  `cap_cliente` varchar(255) NOT NULL,
  `key_cliente` varchar(255) NOT NULL,
  `iva_cliente` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cover`
--

CREATE TABLE `cover` (
  `id_cover` int NOT NULL,
  `id_cliente` int NOT NULL,
  `cellulare` varchar(255) NOT NULL,
  `img_cover` varchar(255) NOT NULL,
  `dipendente` varchar(255) NOT NULL,
  `data_cover` date NOT NULL,
  `status_cover` varchar(50) NOT NULL,
  `origine_cover` text NOT NULL,
  `ordine_cover` int NOT NULL,
  `email_cover` text NOT NULL,
  `cliente_cover` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ddt`
--

CREATE TABLE `ddt` (
  `id_ddt` int NOT NULL,
  `data_ddt` datetime NOT NULL,
  `lab_ddt` int NOT NULL,
  `causale_ddt` varchar(255) NOT NULL,
  `annotazioni_ddt` varchar(255) NOT NULL,
  `modo_ddt` varchar(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ddt_cell`
--

CREATE TABLE `ddt_cell` (
  `id_ddt_cell` int NOT NULL,
  `ddt_ticket` int NOT NULL,
  `ddt_num` int NOT NULL,
  `ddt_cellulare` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `dipendenti`
--

CREATE TABLE `dipendenti` (
  `id_dipendente` int NOT NULL,
  `nome_dipendente` varchar(266) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `firma`
--

CREATE TABLE `firma` (
  `id_firma` int NOT NULL,
  `cliente_firma` varchar(255) NOT NULL,
  `id_cliente` int DEFAULT NULL,
  `img_firma` varchar(255) NOT NULL,
  `data_firma` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `laboratori`
--

CREATE TABLE `laboratori` (
  `id_lab` int NOT NULL,
  `nome_lab` varchar(255) NOT NULL,
  `marca_lab` varchar(255) NOT NULL,
  `indirizzo_lab` varchar(255) NOT NULL,
  `citta_lab` varchar(20) NOT NULL,
  `prov_lab` varchar(3) NOT NULL,
  `cap_lab` varchar(6) NOT NULL,
  `piva_lab` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `log_riscossione`
--

CREATE TABLE `log_riscossione` (
  `id_log` int NOT NULL,
  `id_cliente` int NOT NULL,
  `data_risc` date NOT NULL,
  `somma_risc` int NOT NULL,
  `modo_risc` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `prenotazioni`
--

CREATE TABLE `prenotazioni` (
  `id_prenotazione` int NOT NULL,
  `from_prenotazione` varchar(10) NOT NULL DEFAULT 'Bancone',
  `nome_prenotazione` varchar(255) NOT NULL,
  `data_prenotazione` date NOT NULL,
  `email_prenotazione` varchar(255) NOT NULL,
  `tel_prenotazione` varchar(30) NOT NULL,
  `cell_prenotazione` varchar(255) NOT NULL,
  `memoria_prenotazione` varchar(20) NOT NULL,
  `dipendente` varchar(50) NOT NULL,
  `status_prenotazione` varchar(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `punti`
--

CREATE TABLE `punti` (
  `id_punti` int NOT NULL,
  `cliente_punti` int NOT NULL,
  `num_punti` int NOT NULL,
  `data_punti` date NOT NULL,
  `dip_punti` varchar(255) NOT NULL,
  `status_punti` int NOT NULL DEFAULT '0',
  `causale_punti` varchar(255) NOT NULL,
  `scontrino_punti` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `recensioni`
--

CREATE TABLE `recensioni` (
  `id_recensione` int NOT NULL,
  `nome_recensione` varchar(255) DEFAULT NULL,
  `email_recensione` varchar(255) DEFAULT NULL,
  `tipo_recensione` varchar(255) NOT NULL,
  `data_recensione` date NOT NULL DEFAULT '2023-03-29'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rimborso`
--

CREATE TABLE `rimborso` (
  `rimborso_id` int NOT NULL,
  `rimborso_nome` varchar(255) NOT NULL,
  `rimborso_email` varchar(255) NOT NULL,
  `rimborso_tel` int NOT NULL,
  `rimborso_ordine` int NOT NULL,
  `rimborso_data` date NOT NULL,
  `rimborso_prodotto` varchar(255) NOT NULL,
  `rimborso_motivazione` text NOT NULL,
  `rimborso_modo` varchar(255) NOT NULL,
  `rimborso_intest_iban` varchar(255) NOT NULL,
  `rimborso_iban` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `utente`
--

CREATE TABLE `utente` (
  `id` int NOT NULL,
  `nome` varchar(288) NOT NULL,
  `pass` varchar(288) NOT NULL,
  `role` int NOT NULL,
  `ruolo` varchar(255) NOT NULL,
  `password_changed_at` timestamp NULL DEFAULT NULL,
  `failed_login_attempts` int DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Essential user data for system access
-- IMPORTANT: Change these passwords in production!
--

INSERT INTO `utente` (`id`, `nome`, `pass`, `role`, `ruolo`, `created_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 'Amministratore', NOW()),
(2, 'manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 2, 'Manager', NOW()),
(3, 'developer', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 3, 'Sviluppatore', NOW());

-- Default password for all users is 'password' - CHANGE THIS IN PRODUCTION!

-- --------------------------------------------------------

--
-- Table structure for table `valutazione`
--

CREATE TABLE `valutazione` (
  `id_valutazione` int NOT NULL,
  `time_valutazione` date DEFAULT NULL,
  `nome_valutazione` varchar(255) NOT NULL,
  `email_valutazione` varchar(255) NOT NULL,
  `tel_valutazione` varchar(255) NOT NULL,
  `marca_valutazione` varchar(255) NOT NULL,
  `modello_valutazione` varchar(255) NOT NULL,
  `memoria_valutazione` varchar(255) NOT NULL,
  `data_valutazione` varchar(20) NOT NULL,
  `garanzia_valutazione` varchar(3) NOT NULL,
  `luogo_valutazione` varchar(255) NOT NULL,
  `condizioni_valutazione` varchar(255) NOT NULL,
  `danni_valutazione` varchar(60) NOT NULL,
  `paga_valutazione` varchar(50) NOT NULL,
  `status_valutazione` varchar(50) NOT NULL DEFAULT 'Aperto',
  `key_valutazione` varchar(255) NOT NULL,
  `prezzo_valutazione` int NOT NULL,
  `dipendente_valutazione` varchar(255) NOT NULL,
  `note_valutazione` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `valutazione_banco`
--

CREATE TABLE `valutazione_banco` (
  `id_valutazione` int NOT NULL,
  `time_valutazione` date NOT NULL DEFAULT '1980-01-01',
  `cliente_valutazione` varchar(255) NOT NULL,
  `dipendente_valutazione` varchar(255) NOT NULL,
  `marca_valutazione` varchar(255) NOT NULL,
  `modello_valutazione` varchar(255) NOT NULL,
  `memoria_valutazione` varchar(255) NOT NULL,
  `data_valutazione` varchar(20) NOT NULL,
  `garanzia_valutazione` varchar(3) NOT NULL,
  `luogo_valutazione` varchar(255) NOT NULL,
  `condizioni_valutazione` varchar(255) NOT NULL,
  `danni_valutazione` varchar(60) NOT NULL,
  `paga_valutazione` varchar(50) NOT NULL,
  `note_valutazione` varchar(255) NOT NULL,
  `risc_valutazione` varchar(50) NOT NULL,
  `prezzo_valutazione` int NOT NULL,
  `status_valutazione` varchar(50) NOT NULL DEFAULT 'Aperto'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `valutazione_chat`
--

CREATE TABLE `valutazione_chat` (
  `id_ch` int NOT NULL,
  `mittente` varchar(60) NOT NULL,
  `key_chat` varchar(60) NOT NULL,
  `datach` datetime NOT NULL,
  `testo` text NOT NULL,
  `allegato` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- SECURITY ENHANCEMENT TABLES
--

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `attempt_time` int NOT NULL,
  `user_agent` text,
  `success` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_attempt_time` (`attempt_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `session_id` varchar(128) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `last_activity` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `audit_log`
--

CREATE TABLE `audit_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(100) DEFAULT NULL,
  `record_id` int DEFAULT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- PRIMARY KEYS AND AUTO_INCREMENT
--

ALTER TABLE `acquisto_amico`
  ADD PRIMARY KEY (`id_acq`),
  ADD KEY `idx_cliente` (`id_cliente`),
  ADD KEY `idx_amico` (`id_amico`),
  ADD KEY `idx_data` (`data_acq`);

ALTER TABLE `acquisto_weekbuy`
  ADD PRIMARY KEY (`id_acq_wb`),
  ADD KEY `idx_cliente` (`id_cliente`),
  ADD KEY `idx_data` (`data_acq_wb`),
  ADD KEY `idx_status` (`status_acq_wb`);

ALTER TABLE `assicurazione`
  ADD PRIMARY KEY (`id_assicurazione`);

ALTER TABLE `assicurazione_cliente`
  ADD PRIMARY KEY (`id_assicurazione_cliente`),
  ADD KEY `idx_cliente` (`id_cliente`),
  ADD KEY `idx_assicurazione` (`id_assicurazione`),
  ADD KEY `idx_data_reg` (`data_registrazione`);

ALTER TABLE `assistenza`
  ADD PRIMARY KEY (`id_assistenza`),
  ADD KEY `idx_cliente` (`id_cliente_assistenza`),
  ADD KEY `idx_data` (`data_assistenza`),
  ADD KEY `idx_status` (`status_assistenza`),
  ADD KEY `idx_lab` (`lab_assistenza`);

ALTER TABLE `assistenza_cell`
  ADD PRIMARY KEY (`id_ass_cell`),
  ADD KEY `idx_code` (`code_ass_cell`),
  ADD KEY `idx_marca` (`marca_ass_cell`);

ALTER TABLE `buoni`
  ADD PRIMARY KEY (`id_buono`),
  ADD KEY `idx_acquisto` (`id_acquisto`),
  ADD KEY `idx_cliente` (`id_cliente_buono`);

ALTER TABLE `buoni_banco`
  ADD PRIMARY KEY (`id_buono`),
  ADD KEY `idx_cliente` (`id_cliente`),
  ADD KEY `idx_data` (`databuono`);

ALTER TABLE `cellulare`
  ADD PRIMARY KEY (`id_cell`),
  ADD KEY `idx_nome` (`nome_cell`);

ALTER TABLE `cliente`
  ADD PRIMARY KEY (`id_cliente`),
  ADD KEY `idx_nome` (`nome_cliente`),
  ADD KEY `idx_telefono` (`telefono_cliente`),
  ADD KEY `idx_email` (`email_cliente`),
  ADD KEY `idx_data` (`data_cliente`);

ALTER TABLE `cover`
  ADD PRIMARY KEY (`id_cover`),
  ADD KEY `idx_cliente` (`id_cliente`),
  ADD KEY `idx_data` (`data_cover`);

ALTER TABLE `ddt`
  ADD PRIMARY KEY (`id_ddt`),
  ADD KEY `idx_data` (`data_ddt`),
  ADD KEY `idx_lab` (`lab_ddt`);

ALTER TABLE `ddt_cell`
  ADD PRIMARY KEY (`id_ddt_cell`),
  ADD KEY `idx_ticket` (`ddt_ticket`),
  ADD KEY `idx_num` (`ddt_num`);

ALTER TABLE `dipendenti`
  ADD PRIMARY KEY (`id_dipendente`);

ALTER TABLE `firma`
  ADD PRIMARY KEY (`id_firma`),
  ADD KEY `idx_cliente` (`id_cliente`),
  ADD KEY `idx_data` (`data_firma`);

ALTER TABLE `laboratori`
  ADD PRIMARY KEY (`id_lab`);

ALTER TABLE `log_riscossione`
  ADD PRIMARY KEY (`id_log`),
  ADD KEY `idx_cliente` (`id_cliente`),
  ADD KEY `idx_data` (`data_risc`);

ALTER TABLE `prenotazioni`
  ADD PRIMARY KEY (`id_prenotazione`),
  ADD KEY `idx_data` (`data_prenotazione`),
  ADD KEY `idx_status` (`status_prenotazione`);

ALTER TABLE `punti`
  ADD PRIMARY KEY (`id_punti`),
  ADD KEY `idx_cliente` (`cliente_punti`),
  ADD KEY `idx_data` (`data_punti`);

ALTER TABLE `recensioni`
  ADD PRIMARY KEY (`id_recensione`),
  ADD KEY `idx_data` (`data_recensione`);

ALTER TABLE `rimborso`
  ADD PRIMARY KEY (`rimborso_id`),
  ADD KEY `idx_data` (`rimborso_data`);

ALTER TABLE `utente`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nome` (`nome`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_last_login` (`last_login`);

ALTER TABLE `valutazione`
  ADD PRIMARY KEY (`id_valutazione`),
  ADD KEY `idx_data` (`time_valutazione`),
  ADD KEY `idx_status` (`status_valutazione`),
  ADD KEY `idx_key` (`key_valutazione`);

ALTER TABLE `valutazione_banco`
  ADD PRIMARY KEY (`id_valutazione`),
  ADD KEY `idx_data` (`time_valutazione`),
  ADD KEY `idx_status` (`status_valutazione`);

ALTER TABLE `valutazione_chat`
  ADD PRIMARY KEY (`id_ch`),
  ADD KEY `idx_key` (`key_chat`),
  ADD KEY `idx_data` (`datach`);

-- --------------------------------------------------------

--
-- AUTO_INCREMENT for tables
--

ALTER TABLE `acquisto_amico`
  MODIFY `id_acq` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `acquisto_weekbuy`
  MODIFY `id_acq_wb` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `assicurazione`
  MODIFY `id_assicurazione` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `assicurazione_cliente`
  MODIFY `id_assicurazione_cliente` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `assistenza`
  MODIFY `id_assistenza` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `assistenza_cell`
  MODIFY `id_ass_cell` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `buoni`
  MODIFY `id_buono` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `buoni_banco`
  MODIFY `id_buono` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `cellulare`
  MODIFY `id_cell` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `cliente`
  MODIFY `id_cliente` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `cover`
  MODIFY `id_cover` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `ddt`
  MODIFY `id_ddt` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `ddt_cell`
  MODIFY `id_ddt_cell` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `dipendenti`
  MODIFY `id_dipendente` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `firma`
  MODIFY `id_firma` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `laboratori`
  MODIFY `id_lab` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `log_riscossione`
  MODIFY `id_log` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `prenotazioni`
  MODIFY `id_prenotazione` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `punti`
  MODIFY `id_punti` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `recensioni`
  MODIFY `id_recensione` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `rimborso`
  MODIFY `rimborso_id` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `utente`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `valutazione`
  MODIFY `id_valutazione` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `valutazione_banco`
  MODIFY `id_valutazione` int NOT NULL AUTO_INCREMENT;

ALTER TABLE `valutazione_chat`
  MODIFY `id_ch` int NOT NULL AUTO_INCREMENT;

-- --------------------------------------------------------

--
-- PERFORMANCE OPTIMIZATION INDEXES
-- These indexes improve query performance for frequently accessed data
--

-- Composite indexes for common query patterns
CREATE INDEX `idx_assistenza_cliente_data` ON `assistenza` (`id_cliente_assistenza`, `data_assistenza`);
CREATE INDEX `idx_assistenza_status_data` ON `assistenza` (`status_assistenza`, `data_assistenza`);
CREATE INDEX `idx_cliente_nome_telefono` ON `cliente` (`nome_cliente`, `telefono_cliente`);
CREATE INDEX `idx_punti_cliente_data` ON `punti` (`cliente_punti`, `data_punti`);
CREATE INDEX `idx_acquisto_cliente_data` ON `acquisto_weekbuy` (`id_cliente`, `data_acq_wb`);

-- Full-text search indexes for better search performance
-- ALTER TABLE `cliente` ADD FULLTEXT(`nome_cliente`, `email_cliente`);
-- ALTER TABLE `assistenza` ADD FULLTEXT(`annotazioni_assistenza`, `difetto_assistenza`);

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

-- --------------------------------------------------------
-- PRODUCTION SETUP NOTES
-- --------------------------------------------------------
--
-- 1. SECURITY:
--    - Change all default passwords immediately
--    - Update database credentials in core/config.php
--    - Set proper file permissions (600 for config files)
--
-- 2. PERFORMANCE:
--    - Monitor slow query log
--    - Consider adding more indexes based on actual usage patterns
--    - Implement query result caching for frequently accessed data
--
-- 3. MAINTENANCE:
--    - Set up regular database backups
--    - Monitor database size and implement log rotation
--    - Review and optimize queries periodically
--
-- 4. TESTING:
--    - Test all application functionality with this cleaned database
--    - Verify all forms and reports work correctly
--    - Check that referential integrity is maintained
--
-- END OF CLEANED DATABASE DUMP
