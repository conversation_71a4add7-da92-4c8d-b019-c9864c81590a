# Logs Directory

This directory contains application log files.

## Security Notice

- This directory should be protected from web access
- The .htaccess file denies all web access to log files
- Log files may contain sensitive information
- Regularly rotate and archive old log files
- Monitor log files for security incidents

## Log Files

- `php_errors.log` - PHP errors and warnings
- `app.log` - Application-specific logs
- `security.log` - Security-related events
- `access.log` - User access logs

## Maintenance

- Set up log rotation to prevent files from growing too large
- Archive old logs regularly
- Monitor disk space usage
- Review logs regularly for security issues
