-- ============================================================================
-- OPTIMIZED QUERIES FOR CENTRO TELEFONIA NOLI MANAGEMENT SYSTEM
-- ============================================================================
-- This file contains optimized versions of the most problematic queries
-- identified in the performance analysis.

-- ============================================================================
-- 1. CRITICAL N+1 QUERY FIXES
-- ============================================================================

-- ORIGINAL PROBLEM: pages/report/report_ddt_scheda.php (Lines 68-93)
-- Multiple queries in loop causing N+1 problem

-- OPTIMIZED DDT REPORT QUERY
-- Replaces the N+1 query pattern with a single efficient JOIN
SELECT 
    ddt_cell.id_ddt_cell,
    ddt_cell.ddt_ticket,
    ddt_cell.ddt_num,
    ddt_cell.ddt_cellulare,
    ddt.data_ddt AS data_dt,
    assistenza.id_assistenza AS id_ass,
    cliente.nome_cliente AS nome_cl,
    cellulare.id_cell AS id_cel,
    cellulare.nome_cell AS nome_cel,
    assistenza_cell.marca_ass_cell,
    assistenza_cell.nome_ass_cell,
    assistenza_cell.code_ass_cell
FROM ddt_cell 
LEFT JOIN ddt ON ddt_cell.ddt_num = ddt.id_ddt
LEFT JOIN assistenza ON ddt_cell.ddt_ticket = assistenza.id_assistenza
LEFT JOIN cellulare ON ddt_cell.ddt_cellulare = cellulare.id_cell
LEFT JOIN cliente ON assistenza.id_cliente_assistenza = cliente.id_cliente
LEFT JOIN assistenza_cell ON ddt_cell.ddt_cellulare = assistenza_cell.id_ass_cell
WHERE ddt.data_ddt BETWEEN ? AND ?
  AND ddt.lab_ddt = ?
ORDER BY ddt.data_ddt DESC, ddt_cell.id_ddt_cell ASC;

-- ============================================================================
-- 2. OPTIMIZED LIST QUERIES WITH PAGINATION
-- ============================================================================

-- OPTIMIZED CLIENT LIST QUERY (pages/weekbuy/lista_clienti.php)
-- Adds pagination and specific column selection
SELECT 
    cliente.id_cliente,
    cliente.nome_cliente,
    cliente.data_cliente,
    cliente.telefono_cliente,
    cellulare.nome_cell
FROM cliente 
LEFT JOIN cellulare ON cliente.id_telefono = cellulare.id_cell 
ORDER BY cliente.data_cliente DESC
LIMIT ? OFFSET ?;

-- CLIENT SEARCH QUERY (with prepared statement)
SELECT 
    cliente.id_cliente,
    cliente.nome_cliente,
    cliente.data_cliente,
    cliente.telefono_cliente,
    cellulare.nome_cell
FROM cliente 
LEFT JOIN cellulare ON cliente.id_telefono = cellulare.id_cell 
WHERE cliente.nome_cliente LIKE CONCAT('%', ?, '%')
   OR cliente.telefono_cliente LIKE CONCAT('%', ?, '%')
ORDER BY cliente.data_cliente DESC
LIMIT ? OFFSET ?;

-- OPTIMIZED TICKET LIST QUERY (pages/tecnico/lista_ticket.php)
-- Reduces data transfer and improves performance
SELECT 
    assistenza.id_assistenza AS l_idassistenza,
    assistenza.data_assistenza AS l_data,
    assistenza.status_assistenza AS l_status,
    assistenza.difetto_assistenza AS l_difetto,
    assistenza.prezzo_assistenza AS l_prezzo,
    assistenza.imei_assistenza AS l_imei,
    cliente.id_cliente AS l_idcliente,
    cliente.nome_cliente AS l_nomecliente,
    assistenza_cell.nome_ass_cell AS l_nomecell,
    assistenza_cell.code_ass_cell AS l_codecell,
    assistenza_cell.marca_ass_cell AS l_marcacell,
    laboratori.nome_lab AS l_nomelaboratorio
FROM assistenza
LEFT JOIN cliente ON assistenza.id_cliente_assistenza = cliente.id_cliente 
LEFT JOIN assistenza_cell ON assistenza.id_cell_assistenza = assistenza_cell.id_ass_cell 		
LEFT JOIN laboratori ON assistenza.lab_assistenza = laboratori.id_lab 															
ORDER BY assistenza.id_assistenza DESC
LIMIT ? OFFSET ?;

-- OPTIMIZED USER LIST QUERY (pages/utenti/lista_utenti.php)
SELECT 
    cliente.id_cliente,
    cliente.nome_cliente,
    cliente.data_cliente,
    cliente.email_cliente,
    cliente.telefono_cliente
FROM cliente
ORDER BY cliente.nome_cliente ASC
LIMIT ? OFFSET ?;

-- ============================================================================
-- 3. OPTIMIZED SEARCH QUERIES
-- ============================================================================

-- FULL-TEXT SEARCH FOR CLIENTS (when full-text indexes are added)
SELECT 
    cliente.id_cliente,
    cliente.nome_cliente,
    cliente.telefono_cliente,
    cliente.email_cliente,
    MATCH(cliente.nome_cliente, cliente.email_cliente) AGAINST(? IN NATURAL LANGUAGE MODE) AS relevance
FROM cliente
WHERE MATCH(cliente.nome_cliente, cliente.email_cliente) AGAINST(? IN NATURAL LANGUAGE MODE)
ORDER BY relevance DESC, cliente.nome_cliente ASC
LIMIT ? OFFSET ?;

-- OPTIMIZED ASSISTANCE SEARCH
SELECT 
    assistenza.id_assistenza,
    assistenza.data_assistenza,
    assistenza.status_assistenza,
    cliente.nome_cliente,
    assistenza_cell.nome_ass_cell,
    assistenza_cell.marca_ass_cell
FROM assistenza
LEFT JOIN cliente ON assistenza.id_cliente_assistenza = cliente.id_cliente
LEFT JOIN assistenza_cell ON assistenza.id_cell_assistenza = assistenza_cell.id_ass_cell
WHERE assistenza.status_assistenza = ?
  AND assistenza.data_assistenza BETWEEN ? AND ?
ORDER BY assistenza.data_assistenza DESC
LIMIT ? OFFSET ?;

-- ============================================================================
-- 4. DASHBOARD SUMMARY QUERIES (OPTIMIZED)
-- ============================================================================

-- QUICK STATS FOR DASHBOARD (single query instead of multiple)
SELECT 
    (SELECT COUNT(*) FROM assistenza WHERE status_assistenza = 'Aperto') as tickets_aperti,
    (SELECT COUNT(*) FROM assistenza WHERE status_assistenza = 'Chiuso' AND DATE(data_assistenza) = CURDATE()) as tickets_oggi,
    (SELECT COUNT(*) FROM cliente WHERE DATE(data_cliente) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as clienti_mese,
    (SELECT COUNT(*) FROM acquisto_weekbuy WHERE DATE(data_acq_wb) = CURDATE()) as acquisti_oggi,
    (SELECT SUM(prezzo_assistenza) FROM assistenza WHERE DATE(data_assistenza) = CURDATE()) as incasso_oggi;

-- ============================================================================
-- 5. REPORT QUERIES (OPTIMIZED)
-- ============================================================================

-- MONTHLY ASSISTANCE REPORT
SELECT 
    DATE_FORMAT(assistenza.data_assistenza, '%Y-%m') as mese,
    COUNT(*) as totale_ticket,
    SUM(CASE WHEN status_assistenza = 'Chiuso' THEN 1 ELSE 0 END) as ticket_chiusi,
    SUM(prezzo_assistenza) as incasso_totale,
    AVG(prezzo_assistenza) as prezzo_medio
FROM assistenza
WHERE assistenza.data_assistenza >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
GROUP BY DATE_FORMAT(assistenza.data_assistenza, '%Y-%m')
ORDER BY mese DESC;

-- CUSTOMER PURCHASE HISTORY (optimized for customer detail page)
SELECT 
    acquisto_weekbuy.id_acq_wb,
    acquisto_weekbuy.data_acq_wb,
    acquisto_weekbuy.prodotto,
    acquisto_weekbuy.somma,
    acquisto_weekbuy.status_acq_wb
FROM acquisto_weekbuy
WHERE acquisto_weekbuy.id_cliente = ?
ORDER BY acquisto_weekbuy.data_acq_wb DESC
LIMIT 20;

-- POINTS HISTORY FOR CUSTOMER
SELECT 
    punti.data_punti,
    punti.num_punti,
    punti.causale_punti,
    punti.dip_punti
FROM punti
WHERE punti.cliente_punti = ?
  AND punti.status_punti = 0
ORDER BY punti.data_punti DESC
LIMIT 50;

-- ============================================================================
-- 6. PERFORMANCE MONITORING QUERIES
-- ============================================================================

-- Query to identify slow queries (for monitoring)
SELECT 
    SUBSTRING(sql_text, 1, 100) as query_start,
    exec_count,
    avg_timer_wait/1000000000 as avg_time_seconds,
    sum_timer_wait/1000000000 as total_time_seconds
FROM performance_schema.events_statements_summary_by_digest
WHERE avg_timer_wait > 1000000000  -- queries taking more than 1 second
ORDER BY avg_timer_wait DESC
LIMIT 20;

-- ============================================================================
-- USAGE NOTES:
-- ============================================================================
-- 1. All queries use prepared statements (?) for security
-- 2. LIMIT and OFFSET should be implemented for pagination
-- 3. Indexes from cvcentro_admin_clean.sql support these queries
-- 4. Monitor query performance using the monitoring query above
-- 5. Consider implementing query result caching for frequently accessed data
-- ============================================================================
