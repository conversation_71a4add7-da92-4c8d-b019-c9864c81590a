<?php
/**
 * Query Optimizer Class
 * Provides optimized database queries with pagination, caching, and performance monitoring
 * 
 * <AUTHOR> Telefonia Noli - Security Enhancement Project
 * @version 1.0
 */

class QueryOptimizer {
    private $conn;
    private $cache = [];
    private $cache_ttl = 300; // 5 minutes cache TTL
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get paginated client list with optimized query
     * Fixes N+1 problem in pages/weekbuy/lista_clienti.php
     */
    public function getClientList($page = 1, $limit = 50, $search = null) {
        $offset = ($page - 1) * $limit;
        
        if ($search) {
            $sql = "SELECT 
                        cliente.id_cliente,
                        cliente.nome_cliente,
                        cliente.data_cliente,
                        cliente.telefono_cliente,
                        cellulare.nome_cell
                    FROM cliente 
                    LEFT JOIN cellulare ON cliente.id_telefono = cellulare.id_cell 
                    WHERE cliente.nome_cliente LIKE CONCAT('%', ?, '%')
                       OR cliente.telefono_cliente LIKE CONCAT('%', ?, '%')
                    ORDER BY cliente.data_cliente DESC
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("ssii", $search, $search, $limit, $offset);
        } else {
            $sql = "SELECT 
                        cliente.id_cliente,
                        cliente.nome_cliente,
                        cliente.data_cliente,
                        cliente.telefono_cliente,
                        cellulare.nome_cell
                    FROM cliente 
                    LEFT JOIN cellulare ON cliente.id_telefono = cellulare.id_cell 
                    ORDER BY cliente.data_cliente DESC
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("ii", $limit, $offset);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        return [
            'data' => $result->fetch_all(MYSQLI_ASSOC),
            'total' => $this->getClientCount($search),
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($this->getClientCount($search) / $limit)
        ];
    }
    
    /**
     * Get total client count for pagination
     */
    private function getClientCount($search = null) {
        $cache_key = 'client_count_' . md5($search ?? '');
        
        if (isset($this->cache[$cache_key]) && 
            $this->cache[$cache_key]['expires'] > time()) {
            return $this->cache[$cache_key]['data'];
        }
        
        if ($search) {
            $sql = "SELECT COUNT(*) as total FROM cliente 
                    WHERE cliente.nome_cliente LIKE CONCAT('%', ?, '%')
                       OR cliente.telefono_cliente LIKE CONCAT('%', ?, '%')";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("ss", $search, $search);
        } else {
            $sql = "SELECT COUNT(*) as total FROM cliente";
            $stmt = $this->conn->prepare($sql);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_assoc()['total'];
        
        // Cache the result
        $this->cache[$cache_key] = [
            'data' => $count,
            'expires' => time() + $this->cache_ttl
        ];
        
        return $count;
    }
    
    /**
     * Get optimized ticket list
     * Fixes performance issues in pages/tecnico/lista_ticket.php
     */
    public function getTicketList($page = 1, $limit = 50, $status = null) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT 
                    assistenza.id_assistenza AS l_idassistenza,
                    assistenza.data_assistenza AS l_data,
                    assistenza.status_assistenza AS l_status,
                    assistenza.difetto_assistenza AS l_difetto,
                    assistenza.prezzo_assistenza AS l_prezzo,
                    assistenza.imei_assistenza AS l_imei,
                    cliente.id_cliente AS l_idcliente,
                    cliente.nome_cliente AS l_nomecliente,
                    assistenza_cell.nome_ass_cell AS l_nomecell,
                    assistenza_cell.code_ass_cell AS l_codecell,
                    assistenza_cell.marca_ass_cell AS l_marcacell,
                    laboratori.nome_lab AS l_nomelaboratorio
                FROM assistenza
                LEFT JOIN cliente ON assistenza.id_cliente_assistenza = cliente.id_cliente 
                LEFT JOIN assistenza_cell ON assistenza.id_cell_assistenza = assistenza_cell.id_ass_cell 		
                LEFT JOIN laboratori ON assistenza.lab_assistenza = laboratori.id_lab";
        
        if ($status) {
            $sql .= " WHERE assistenza.status_assistenza = ?";
        }
        
        $sql .= " ORDER BY assistenza.id_assistenza DESC LIMIT ? OFFSET ?";
        
        $stmt = $this->conn->prepare($sql);
        
        if ($status) {
            $stmt->bind_param("sii", $status, $limit, $offset);
        } else {
            $stmt->bind_param("ii", $limit, $offset);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        return [
            'data' => $result->fetch_all(MYSQLI_ASSOC),
            'total' => $this->getTicketCount($status),
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($this->getTicketCount($status) / $limit)
        ];
    }
    
    /**
     * Get optimized DDT report - FIXES CRITICAL N+1 PROBLEM
     * Replaces the problematic query in pages/report/report_ddt_scheda.php
     */
    public function getDDTReport($data_inizio, $data_fine, $nome_laboratorio) {
        $cache_key = 'ddt_report_' . md5($data_inizio . $data_fine . $nome_laboratorio);
        
        if (isset($this->cache[$cache_key]) && 
            $this->cache[$cache_key]['expires'] > time()) {
            return $this->cache[$cache_key]['data'];
        }
        
        $sql = "SELECT 
                    ddt_cell.id_ddt_cell,
                    ddt_cell.ddt_ticket,
                    ddt_cell.ddt_num,
                    ddt_cell.ddt_cellulare,
                    ddt.data_ddt AS data_dt,
                    assistenza.id_assistenza AS id_ass,
                    cliente.nome_cliente AS nome_cl,
                    cellulare.id_cell AS id_cel,
                    cellulare.nome_cell AS nome_cel,
                    assistenza_cell.marca_ass_cell,
                    assistenza_cell.nome_ass_cell,
                    assistenza_cell.code_ass_cell
                FROM ddt_cell 
                LEFT JOIN ddt ON ddt_cell.ddt_num = ddt.id_ddt
                LEFT JOIN assistenza ON ddt_cell.ddt_ticket = assistenza.id_assistenza
                LEFT JOIN cellulare ON ddt_cell.ddt_cellulare = cellulare.id_cell
                LEFT JOIN cliente ON assistenza.id_cliente_assistenza = cliente.id_cliente
                LEFT JOIN assistenza_cell ON ddt_cell.ddt_cellulare = assistenza_cell.id_ass_cell
                WHERE ddt.data_ddt BETWEEN ? AND ?
                  AND ddt.lab_ddt = ?
                ORDER BY ddt.data_ddt DESC, ddt_cell.id_ddt_cell ASC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("sss", $data_inizio, $data_fine, $nome_laboratorio);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $data = $result->fetch_all(MYSQLI_ASSOC);
        
        // Cache the result for 10 minutes (reports don't change frequently)
        $this->cache[$cache_key] = [
            'data' => $data,
            'expires' => time() + 600
        ];
        
        return $data;
    }
    
    /**
     * Get dashboard statistics with single optimized query
     */
    public function getDashboardStats() {
        $cache_key = 'dashboard_stats';
        
        if (isset($this->cache[$cache_key]) && 
            $this->cache[$cache_key]['expires'] > time()) {
            return $this->cache[$cache_key]['data'];
        }
        
        $sql = "SELECT 
                    (SELECT COUNT(*) FROM assistenza WHERE status_assistenza = 'Aperto') as tickets_aperti,
                    (SELECT COUNT(*) FROM assistenza WHERE status_assistenza = 'Chiuso' AND DATE(data_assistenza) = CURDATE()) as tickets_oggi,
                    (SELECT COUNT(*) FROM cliente WHERE DATE(data_cliente) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as clienti_mese,
                    (SELECT COUNT(*) FROM acquisto_weekbuy WHERE DATE(data_acq_wb) = CURDATE()) as acquisti_oggi,
                    (SELECT COALESCE(SUM(prezzo_assistenza), 0) FROM assistenza WHERE DATE(data_assistenza) = CURDATE()) as incasso_oggi";
        
        $result = $this->conn->query($sql);
        $stats = $result->fetch_assoc();
        
        // Cache for 5 minutes
        $this->cache[$cache_key] = [
            'data' => $stats,
            'expires' => time() + 300
        ];
        
        return $stats;
    }
    
    /**
     * Get customer purchase history (optimized for customer detail pages)
     */
    public function getCustomerPurchases($customer_id, $limit = 20) {
        $sql = "SELECT 
                    acquisto_weekbuy.id_acq_wb,
                    acquisto_weekbuy.data_acq_wb,
                    acquisto_weekbuy.prodotto,
                    acquisto_weekbuy.somma,
                    acquisto_weekbuy.status_acq_wb
                FROM acquisto_weekbuy
                WHERE acquisto_weekbuy.id_cliente = ?
                ORDER BY acquisto_weekbuy.data_acq_wb DESC
                LIMIT ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ii", $customer_id, $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->fetch_all(MYSQLI_ASSOC);
    }
    
    /**
     * Get ticket count for pagination
     */
    private function getTicketCount($status = null) {
        $cache_key = 'ticket_count_' . ($status ?? 'all');
        
        if (isset($this->cache[$cache_key]) && 
            $this->cache[$cache_key]['expires'] > time()) {
            return $this->cache[$cache_key]['data'];
        }
        
        if ($status) {
            $sql = "SELECT COUNT(*) as total FROM assistenza WHERE status_assistenza = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("s", $status);
        } else {
            $sql = "SELECT COUNT(*) as total FROM assistenza";
            $stmt = $this->conn->prepare($sql);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_assoc()['total'];
        
        // Cache the result
        $this->cache[$cache_key] = [
            'data' => $count,
            'expires' => time() + $this->cache_ttl
        ];
        
        return $count;
    }
    
    /**
     * Clear cache (useful for data updates)
     */
    public function clearCache($pattern = null) {
        if ($pattern) {
            foreach ($this->cache as $key => $value) {
                if (strpos($key, $pattern) !== false) {
                    unset($this->cache[$key]);
                }
            }
        } else {
            $this->cache = [];
        }
    }
    
    /**
     * Get query performance statistics
     */
    public function getQueryPerformanceStats() {
        $sql = "SELECT 
                    SUBSTRING(sql_text, 1, 100) as query_start,
                    exec_count,
                    avg_timer_wait/1000000000 as avg_time_seconds,
                    sum_timer_wait/1000000000 as total_time_seconds
                FROM performance_schema.events_statements_summary_by_digest
                WHERE avg_timer_wait > 1000000000
                ORDER BY avg_timer_wait DESC
                LIMIT 20";
        
        $result = $this->conn->query($sql);
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
}

// Usage example:
// $optimizer = new QueryOptimizer($conn);
// $clients = $optimizer->getClientList(1, 50, 'Mario');
// $tickets = $optimizer->getTicketList(1, 25, 'Aperto');
// $stats = $optimizer->getDashboardStats();
?>
